<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionRedirectMiddleware
{
    /**
     * Handle an incoming request.
     *
     * This middleware redirects non-admin users from subscription-related routes
     * to the subscription management interface while preserving admin access.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();
        
        // Check if user is admin - if so, allow access to all routes
        if ($user && str_contains($user->access_module, 'Admin')) {
            return $next($request);
        }

        // Get current route name and path
        $routeName = $request->route()->getName();
        $currentPath = $request->path();

        // Define subscription-related route patterns that should be redirected for non-admin users
        $subscriptionRoutePatterns = [
            'user.subscriptions.index',
            'user.subscriptions.subscribe',
            'user.subscriptions.payment',
            'user.subscriptions.process-payment',
            'user.subscriptions.thankyou',
            'user.addons.index',
            'user.addons.limits',
            'user.addons.show',
            'user.addons.purchase.form',
            'user.addons.purchase.process',
            'user.addons.thankyou',
            'user.addons.cancel',
            'user.addons.cancel.process',
            'user.addons.limit-upgrade.purchase.form',
            'user.addons.limit-upgrade.purchase.process',
        ];

        // Define subscription-related path patterns
        $subscriptionPathPatterns = [
            'subscriptions',
            'subscriptions/',
            'addons',
            'addons/',
        ];

        // Check if current route should be redirected
        $shouldRedirect = false;

        // Check by route name
        if ($routeName && in_array($routeName, $subscriptionRoutePatterns)) {
            $shouldRedirect = true;
        }

        // Check by path pattern for routes that might not have names
        if (!$shouldRedirect) {
            foreach ($subscriptionPathPatterns as $pattern) {
                if ($currentPath === $pattern || str_starts_with($currentPath, $pattern . '/')) {
                    $shouldRedirect = true;
                    break;
                }
            }
        }

        // Redirect to subscription management if needed
        if ($shouldRedirect) {
            // Preserve any query parameters
            $queryString = $request->getQueryString();
            $redirectUrl = route('user.subscriptions.management');
            
            if ($queryString) {
                $redirectUrl .= '?' . $queryString;
            }

            return redirect($redirectUrl)->with('info', 'You have been redirected to the subscription management interface.');
        }

        return $next($request);
    }
}
