<?php

namespace App\Http\Controllers\User;

use Carbon\Carbon;

use App\Models\Feature;
use Illuminate\Http\Request;
use App\Models\Payment\Bizappay;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionPlan;
use App\Http\Controllers\API\BizappayController;
use App\Services\SubscriptionEMandateService;

class SubscriptionController extends Controller
{
    /**
     * Display a listing of available subscription plans.
     */
    public function index()
    {
        $user = Auth::user();
        $company = $user->companies;
        $activeSubscription = null;

        if ($company) {
            $activeSubscription = $company->activeSubscription()->with('subscriptionPlan')->first();
        }

        $plans = SubscriptionPlan::where('is_active', true)
                                ->with('features')
                                ->orderBy('price')
                                ->get();

        return view('user.subscriptions.index', compact('plans', 'activeSubscription', 'company'));
    }

    /**
     * Show the subscription plan details.
     */
    public function show($id)
    {
        $user = Auth::user();
        $company = $user->companies;

        $plan = SubscriptionPlan::with('features')->findOrFail($id);
        return view('user.subscriptions.show', compact('plan','company'));
    }

    /**
     * Subscribe to a plan.
     */
    public function subscribe(Request $request, $planId)
    {
        $plan = SubscriptionPlan::findOrFail($planId);
        $user = Auth::user();
        $company = $user->companies;

        if (!$company) {
            return redirect()->route('company.wizard')
                ->with('error', 'Please complete your company profile first.');
        }

        // Check if there's an active subscription
        $activeSubscription = $company->activeSubscription()->first();
        DB::beginTransaction();
        try {
            // If there's an active subscription, cancel it
            if ($activeSubscription) {
                $activeSubscription->cancel(true);
            }

            // Calculate subscription dates
            $startsAt = Carbon::now();
            $endsAt = null;

            if ($plan->duration_in_seconds > 0) {
                $endsAt = $startsAt->copy()->addSeconds($plan->duration_in_seconds);
            }

            // Calculate trial period if applicable
            $trialEndsAt = null;
            if ($plan->trial_period_days > 0) {
                $trialEndsAt = $startsAt->copy()->addDays($plan->trial_period_days);
                // If there's a trial, the subscription starts after the trial
                $endsAt = $trialEndsAt->copy()->addSeconds($plan->duration_in_seconds);
            }

            // Create new subscription
            $subscription = Subscription::create([
                'company_id' => $company->id,
                'subscription_plan_id' => $plan->id,
                'starts_at' => $startsAt,
                'ends_at' => $endsAt,
                'trial_ends_at' => $trialEndsAt,
                'status' => 'active'
            ]);

            DB::commit();

            // Redirect to payment if needed
            if ($plan->price > 0 && !$plan->trial_period_days) {
                return redirect()->route('user.subscriptions.payment', $subscription->id);
            }

            return redirect()->route('user.subscriptions.thankyou', $subscription->id)
                ->with('success', 'You have successfully subscribed to the ' . $plan->name . ' plan.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Failed to subscribe to the plan: ' . $e->getMessage());
        }
    }

    /**
     * Show the payment page for a subscription.
     */
    public function showPayment($subscriptionId)
    {
        $subscription = Subscription::with('subscriptionPlan')->findOrFail($subscriptionId);
        $user = Auth::user();
        $company = $user->companies;

        if ($subscription->company_id !== $company->id) {
            abort(403, 'Unauthorized action.');
        }

        return view('user.subscriptions.payment', compact('subscription'));
    }

    /**
     * Process the payment for a subscription.
     */
    public function processPayment(Request $request, $subscriptionId, SubscriptionEMandateService $emandateService)
    {
        $subscription = Subscription::with('subscriptionPlan')->findOrFail($subscriptionId);
        $user = Auth::user();
        $company = $user->companies;

        if ($subscription->company_id !== $company->id) {
            abort(403, 'Unauthorized action.');
        }

        // Handle BayarCash E-Mandate payment method (default)
        if ($request->payment_method == 'bayarcash_emandate') {
            // Check if user has complete e-mandate data before proceeding
            $validationService = app(\App\Services\EMandateDataValidationService::class);
            $dataCheck = $validationService->canProceedWithEMandate($user);

            if (!$dataCheck['can_proceed']) {
                Log::info('User data incomplete for e-mandate, redirecting to data collection', [
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'validation_results' => $dataCheck['validation_results']
                ]);

                return redirect()->route('user.emandate.data-collection', [
                    'subscription_id' => $subscription->id,
                    'return_url' => route('user.subscriptions.payment', $subscription->id)
                ])->with('info', 'Please complete your profile information to proceed with Direct Debit setup.');
            }
            try {
                Log::info('Processing BayarCash e-mandate payment for subscription', [
                    'subscription_id' => $subscription->id,
                    'user_id' => $user->id,
                    'company_id' => $company->id
                ]);

                // Create e-mandate enrollment for subscription
                $result = $emandateService->createSubscriptionEMandate($subscription);

                if ($result['success']) {
                    if ($result['is_existing']) {
                        // Existing enrollment found and linked
                        return redirect()->route('user.subscriptions.thankyou', $subscription->id)
                            ->with('success', 'Your subscription has been linked to your existing Direct Debit setup.');
                    } else {
                        // New enrollment created, redirect to BayarCash
                        Log::info('Redirecting to BayarCash e-mandate enrollment', [
                            'subscription_id' => $subscription->id,
                            'enrollment_url' => $result['redirect_url']
                        ]);

                        return redirect($result['redirect_url']);
                    }
                } else {
                    // E-mandate creation failed
                    Log::error('E-mandate enrollment failed for subscription', [
                        'subscription_id' => $subscription->id,
                        'error' => $result['message']
                    ]);

                    return redirect()->route('user.subscriptions.payment', $subscription->id)
                        ->withError('Failed to setup Direct Debit: ' . $result['message'] . '. Please try again or contact customer support.');
                }

            } catch (\Exception $e) {
                Log::error('Exception in BayarCash e-mandate payment processing', [
                    'subscription_id' => $subscription->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                return redirect()->route('user.subscriptions.payment', $subscription->id)
                    ->withError('An unexpected error occurred while setting up Direct Debit. Please try again later or contact customer support.');
            }
        } elseif ($request->payment_method == 'bizappay') {
            try {
                // Get Bizappay token (using sandbox for testing)
                $bizappayController = new BizappayController();

                try {
                    $bizappayToken = $bizappayController->generateToken(true); // true = use sandbox
                } catch (\Exception $e) {
                    // Handle specific error codes from BizappayController
                    $errorCode = $e->getCode();
                    $errorMessage = $e->getMessage();

                    Log::error('Failed to generate Bizappay token', [
                        'error_code' => $errorCode,
                        'error_message' => $errorMessage,
                        'subscription_id' => $subscription->id
                    ]);

                    // Provide user-friendly error messages based on error code
                    switch ($errorCode) {
                        case 1001:
                            // API key owner not found error
                            return redirect()->route('dashboard')
                                ->withError('We are currently experiencing technical issues with our payment provider. Our team has been notified and is working to resolve this. Please try again later or contact customer support.');
                        case 1005:
                            // Connection timeout error
                            return redirect()->route('dashboard')
                                ->withError('Connection to payment gateway timed out. This could be due to network issues. Please try again in a few minutes or contact customer support if the problem persists.');
                        case 1003:
                            // General connection error
                            return redirect()->route('dashboard')
                                ->withError('Unable to connect to payment service. Please check your internet connection and try again later.');
                        default:
                            // Other errors
                            return redirect()->route('dashboard')
                                ->withError('Payment service is temporarily unavailable. Please try again later or contact customer support. Error code: ' . $errorCode);
                    }
                }

                // Get configuration (using sandbox for testing)
                $getApiKey = config('services.bizappay.keySandbox');
                $bizappayUrl = config('services.bizappay.urlSandbox');
                $categoryCode = '4z6h3kuf'; // Sandbox category code

                // Format the amount with 2 decimal places
                $amount = number_format($subscription->subscriptionPlan->price, 2, '.', '');

                try {
                    // Create bill in Bizappay
                    $response = Http::withHeaders([
                        'Authentication' => $bizappayToken,
                    ])
                    ->asForm()
                    ->post($bizappayUrl . 'bill/create', [
                        'apiKey' => $getApiKey,
                        'category' => $categoryCode,
                        'name' => 'Bizappos ' . $subscription->subscriptionPlan->name . ' Subscription',
                        'amount' => $amount,
                        'payer_name' => $user->username,
                        'payer_email' => $user->email,
                        'payer_phone' => $user->userDetail->mobile ?? '',
                        'webreturn_url' => route('payment.subscription.return'),
                        'callback_url' => route('payment.subscription.callback'),
                        'reference_1' => $subscription->id, // Store subscription ID for reference
                    ]);

                    $bizappayPage = $response->json();

                    // Log the response for debugging
                    Log::info('Bizappay bill creation response', ['response' => $bizappayPage]);

                    // Check for error responses
                    if (isset($bizappayPage['status']) && $bizappayPage['status'] === 'error') {
                        $errorMessage = $bizappayPage['msg'] ?? 'Unknown error';

                        Log::error('Bizappay bill creation error', [
                            'error_message' => $errorMessage,
                            'subscription_id' => $subscription->id
                        ]);

                        return redirect()->route('dashboard')
                            ->withError('Unable to process payment: ' . $errorMessage . '. Please try again later or contact customer support.');
                    }

                    if ($bizappayPage['status'] == "ok") {
                        // Save billcode to subscription
                        $subscription->update([
                            'bill_id' => $bizappayPage['billCode'],
                            'bill_type' => 'bizappay'
                        ]);

                        // Create Bizappay record
                        Bizappay::create([
                            'billcode' => $bizappayPage['billCode'],
                            'categorycode' => $categoryCode,
                            'paidamount' => $amount,
                            'paymentstatus' => 'Pending'
                        ]);

                        // Redirect to Bizappay payment page
                        return redirect($bizappayPage['url']);
                    } else {
                        Log::error('Failed to create Bizappay bill - unexpected response', [
                            'response' => $bizappayPage,
                            'subscription_id' => $subscription->id
                        ]);

                        return redirect()->route('dashboard')
                            ->withError('We encountered an issue while setting up your payment. Please try again later or contact customer support.');
                    }
                } catch (\Illuminate\Http\Client\RequestException $e) {
                    Log::error('HTTP error during Bizappay bill creation', [
                        'error' => $e->getMessage(),
                        'status' => $e->getCode(),
                        'subscription_id' => $subscription->id
                    ]);

                    return redirect()->route('dashboard')
                        ->withError('Unable to connect to payment service. Please check your internet connection and try again later.');
                }
            } catch (\Exception $e) {
                Log::error('Unexpected exception in subscription payment process', [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'subscription_id' => $subscription->id
                ]);

                return redirect()->route('dashboard')
                    ->withError('An unexpected error occurred while processing your payment. Our team has been notified. Please try again later or contact customer support.');
            }
        } else {
            $subscription->update([
                'bill_id' => 'MANUAL-' . time(),
                'bill_type' => 'manual'
            ]);
        }



        return redirect()->route('user.subscriptions.thankyou', $subscription->id)
            ->with('success', 'Payment processed successfully.');
    }

    /**
     * Show the thank you page after subscription.
     */
    public function thankYou($subscriptionId)
    {
        $subscription = Subscription::with('subscriptionPlan')->findOrFail($subscriptionId);
        $user = Auth::user();
        $company = $user->companies;

        if ($subscription->company_id !== $company->id) {
            abort(403, 'Unauthorized action.');
        }

        return view('user.subscriptions.thankyou', compact('subscription'));
    }

    /**
     * Cancel the current subscription.
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();
        $company = $user->companies;

        if (!$company) {
            return redirect()->back()->with('error', 'Company not found.');
        }

        $subscription = $company->activeSubscription()->first();

        if (!$subscription) {
            return redirect()->back()->with('error', 'No active subscription found.');
        }

        $immediately = $request->has('immediately') && $request->immediately;
        $subscription->cancel($immediately);

        return redirect()->route('user.subscriptions.index')
            ->with('success', 'Your subscription has been canceled.');
    }

    /**
     * Resume a canceled subscription.
     */
    public function resume()
    {
        $user = Auth::user();
        $company = $user->companies;

        if (!$company) {
            return redirect()->back()->with('error', 'Company not found.');
        }

        $subscription = $company->subscriptions()
                                ->where('status', 'active')
                                ->where('canceled_at', '!=', null)
                                ->whereNull('ends_at')
                                ->orWhere('ends_at', '>', Carbon::now())
                                ->first();

        if (!$subscription) {
            return redirect()->back()->with('error', 'No canceled subscription found that can be resumed.');
        }

        $subscription->resume();

        return redirect()->route('user.subscriptions.index')
            ->with('success', 'Your subscription has been resumed.');
    }
}